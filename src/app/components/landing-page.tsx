"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { GlassCard, GlassCardContent, GlassCardHeader, GlassCardTitle, GlassCardDescription } from "@/components/ui/glass-card";
import {
  CheckCircle,
  Users,
  Layers,
  Calendar,
  Tag,
  Zap,
  ArrowRight,
  Sparkles
} from "lucide-react";
import { cn } from "@/lib/utils";

export function LandingPage() {
  const [mounted, setMounted] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Trigger animations after mount
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className={cn(
      "min-h-screen bg-background transition-opacity duration-1000",
      isVisible ? "opacity-100" : "opacity-0"
    )}>
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 glass-card border-0 border-b border-border/50 rounded-none">
        <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center space-x-2 sm:space-x-3">
              <Image
                src="/NeoTask_Icon_N.webp"
                alt="NeoTask"
                width={28}
                height={28}
                className="object-contain sm:w-8 sm:h-8"
              />
              <span className="text-lg sm:text-xl font-light tracking-tight">NeoTask</span>
            </div>

            {/* Sign Up Button */}
            <Button asChild size="sm" className="gradient-red-purple text-white font-normal sm:size-default">
              <Link href="/signup">
                <span className="hidden sm:inline">Sign Up</span>
                <span className="sm:hidden">Sign Up</span>
                <ArrowRight className="ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-20 sm:pt-24 pb-12 sm:pb-16 px-4 sm:px-6">
        <div className="w-full max-w-4xl mx-auto">
          <div className={cn(
            "text-center space-y-6 sm:space-y-8 transition-all duration-1000 delay-300",
            isVisible ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"
          )}>
            {/* Logo */}
            <div className="flex justify-center">
              <Image
                src="/NeoTask_Logo_white.webp"
                alt="NeoTask"
                width={100}
                height={100}
                className="object-contain sm:w-[120px] sm:h-[120px]"
                priority
              />
            </div>

            {/* Hero Text */}
            <div className="space-y-3 sm:space-y-4">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-extralight tracking-tight gradient-red-purple gradient-text">
                Task Management
                <br />
                Reimagined
              </h1>
              <p className="text-lg sm:text-xl text-muted-foreground font-light max-w-2xl mx-auto px-4">
                A full-stack learning project exploring modern web development with Next.js, React, and PostgreSQL—built to showcase real-time collaboration, beautiful UI design, and scalable architecture.
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center px-4">
              <Button asChild size="lg" className="gradient-red-purple text-white font-normal w-full sm:w-auto">
                <Link href="/signup">
                  Get Started Free
                  <Sparkles className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="font-normal w-full sm:w-auto">
                <Link href="/signin">
                  Sign In
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* App Screenshots Section */}
      <section className="py-12 sm:py-16 px-4 sm:px-6">
        <div className="w-full max-w-7xl mx-auto">
          <div className="relative">
            {/* Desktop Screenshot - Large Background */}
            <div className="relative w-full">
              <div className="aspect-[16/10] w-full rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-muted/20 to-muted/40 border border-border/50">
                {/* Placeholder for desktop screenshot */}
                <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-background/80 to-muted/20 backdrop-blur-sm">
                  <div className="text-center space-y-4">
                    <div className="w-16 h-16 mx-auto rounded-xl bg-gradient-to-r from-primary to-secondary flex items-center justify-center">
                      <Layers className="h-8 w-8 text-white" />
                    </div>
                    <div className="space-y-2">
                      <p className="text-lg font-medium text-muted-foreground">Desktop App Screenshot</p>
                      <p className="text-sm text-muted-foreground/70">1920x1200 recommended</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Glass overlay effect */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-t from-background/10 via-transparent to-background/5 pointer-events-none"></div>
            </div>

            {/* Mobile Screenshot - Layered on top */}
            <div className="absolute -bottom-8 right-4 sm:right-8 lg:right-16 z-10">
              <div className="relative">
                {/* Mobile device frame */}
                <div className="w-48 sm:w-56 lg:w-64 aspect-[9/19.5] rounded-[2rem] bg-gray-900 p-2 shadow-2xl">
                  <div className="w-full h-full rounded-[1.5rem] overflow-hidden bg-gradient-to-br from-background/90 to-muted/30 backdrop-blur-sm border border-border/30">
                    {/* Placeholder for mobile screenshot */}
                    <div className="w-full h-full flex items-center justify-center">
                      <div className="text-center space-y-3">
                        <div className="w-12 h-12 mx-auto rounded-lg bg-gradient-to-r from-secondary to-accent flex items-center justify-center">
                          <CheckCircle className="h-6 w-6 text-white" />
                        </div>
                        <div className="space-y-1">
                          <p className="text-sm font-medium text-muted-foreground">Mobile App</p>
                          <p className="text-xs text-muted-foreground/70">375x812</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Mobile device details */}
                  <div className="absolute top-6 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gray-800 rounded-full"></div>
                  <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-gray-800 rounded-full"></div>
                </div>

                {/* Mobile screenshot glow effect */}
                <div className="absolute inset-0 rounded-[2rem] bg-gradient-to-t from-primary/20 via-transparent to-secondary/10 pointer-events-none"></div>
              </div>
            </div>
          </div>

          {/* Optional caption */}
          <div className="text-center mt-16 sm:mt-20">
            <p className="text-base sm:text-lg text-muted-foreground font-light">
              Experience NeoTask across all your devices with seamless synchronization
            </p>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-12 sm:py-16 px-4 sm:px-6 bg-muted/10">
        <div className="w-full max-w-4xl mx-auto">
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl font-light tracking-tight mb-3 sm:mb-4">About This Project</h2>
            <p className="text-base sm:text-lg text-muted-foreground font-light px-4">
              A comprehensive full-stack learning journey
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6 sm:gap-8">
            <GlassCard intensity="medium">
              <GlassCardContent className="p-6">
                <h3 className="text-xl font-normal mb-4 gradient-red-purple gradient-text">Why I Built NeoTask</h3>
                <div className="space-y-3 text-muted-foreground">
                  <p>
                    NeoTask was created as a comprehensive learning project to explore modern full-stack development practices.
                    While there are many task management apps available, I wanted to build something that would challenge me
                    to implement complex features like real-time collaboration, advanced UI patterns, and scalable architecture.
                  </p>
                  <p>
                    The project serves as both a functional productivity tool and a showcase of technical skills,
                    demonstrating proficiency in modern web technologies and best practices.
                  </p>
                </div>
              </GlassCardContent>
            </GlassCard>

            <GlassCard intensity="medium">
              <GlassCardContent className="p-6">
                <h3 className="text-xl font-normal mb-4 gradient-red-purple gradient-text">What Makes It Unique</h3>
                <div className="space-y-3 text-muted-foreground">
                  <p>
                    NeoTask stands out with its glass morphism design system, creating a modern and visually appealing interface.
                    The app features real-time collaboration, drag-and-drop interactions, and a sophisticated spaces/lists
                    organization system that goes beyond simple task lists.
                  </p>
                  <p>
                    Built with performance in mind, it implements advanced caching strategies, optimistic updates,
                    and Row Level Security for multi-tenant data isolation—features typically found in enterprise applications.
                  </p>
                </div>
              </GlassCardContent>
            </GlassCard>

            <GlassCard intensity="medium" className="md:col-span-2">
              <GlassCardContent className="p-6">
                <h3 className="text-xl font-normal mb-4 gradient-red-purple gradient-text">Key Learning Objectives Achieved</h3>
                <div className="grid sm:grid-cols-2 gap-4 text-muted-foreground">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                      <span>Modern React patterns with Next.js 15 App Router</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                      <span>Real-time UI updates and optimistic mutations</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                      <span>Advanced database design with RLS and multi-tenancy</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                      <span>Custom design system and component architecture</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                      <span>JWT-based authentication and authorization</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                      <span>Complex drag-and-drop interactions</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                      <span>Performance optimization and caching strategies</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                      <span>Responsive design and mobile-first development</span>
                    </div>
                  </div>
                </div>
              </GlassCardContent>
            </GlassCard>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 sm:py-16 px-4 sm:px-6">
        <div className="w-full max-w-6xl mx-auto">
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl font-light tracking-tight mb-3 sm:mb-4">Everything you need to stay organized</h2>
            <p className="text-base sm:text-lg text-muted-foreground font-light px-4">
              Powerful features designed to help you and your team work more efficiently
            </p>
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {/* Task Management */}
            <GlassCard intensity="medium" className="text-center">
              <GlassCardHeader>
                <div className="flex justify-center mb-4">
                  <div className="p-3 rounded-full bg-primary/10">
                    <CheckCircle className="h-8 w-8 text-primary" />
                  </div>
                </div>
                <GlassCardTitle>Smart Task Management</GlassCardTitle>
                <GlassCardDescription>
                  Create tasks with subtasks, set due dates, and organize everything with drag-and-drop simplicity.
                </GlassCardDescription>
              </GlassCardHeader>
            </GlassCard>

            {/* Collaboration */}
            <GlassCard intensity="medium" className="text-center">
              <GlassCardHeader>
                <div className="flex justify-center mb-4">
                  <div className="p-3 rounded-full bg-secondary/10">
                    <Users className="h-8 w-8 text-secondary" />
                  </div>
                </div>
                <GlassCardTitle>Team Collaboration</GlassCardTitle>
                <GlassCardDescription>
                  Share spaces with your team, collaborate on projects, and keep everyone aligned on goals.
                </GlassCardDescription>
              </GlassCardHeader>
            </GlassCard>

            {/* Spaces & Lists */}
            <GlassCard intensity="medium" className="text-center">
              <GlassCardHeader>
                <div className="flex justify-center mb-4">
                  <div className="p-3 rounded-full bg-accent/10">
                    <Layers className="h-8 w-8 text-accent" />
                  </div>
                </div>
                <GlassCardTitle>Spaces & Lists</GlassCardTitle>
                <GlassCardDescription>
                  Organize projects into spaces and break them down into customizable, color-coded lists.
                </GlassCardDescription>
              </GlassCardHeader>
            </GlassCard>

            {/* Calendar Integration */}
            <GlassCard intensity="medium" className="text-center">
              <GlassCardHeader>
                <div className="flex justify-center mb-4">
                  <div className="p-3 rounded-full bg-primary/10">
                    <Calendar className="h-8 w-8 text-primary" />
                  </div>
                </div>
                <GlassCardTitle>Calendar View</GlassCardTitle>
                <GlassCardDescription>
                  Visualize your tasks and deadlines with an integrated calendar view that keeps you on track.
                </GlassCardDescription>
              </GlassCardHeader>
            </GlassCard>

            {/* Tags & Filtering */}
            <GlassCard intensity="medium" className="text-center">
              <GlassCardHeader>
                <div className="flex justify-center mb-4">
                  <div className="p-3 rounded-full bg-secondary/10">
                    <Tag className="h-8 w-8 text-secondary" />
                  </div>
                </div>
                <GlassCardTitle>Smart Tags</GlassCardTitle>
                <GlassCardDescription>
                  Tag your tasks and filter by categories to quickly find what you're looking for.
                </GlassCardDescription>
              </GlassCardHeader>
            </GlassCard>

            {/* Performance */}
            <GlassCard intensity="medium" className="text-center">
              <GlassCardHeader>
                <div className="flex justify-center mb-4">
                  <div className="p-3 rounded-full bg-accent/10">
                    <Zap className="h-8 w-8 text-accent" />
                  </div>
                </div>
                <GlassCardTitle>Lightning Fast</GlassCardTitle>
                <GlassCardDescription>
                  Built for speed with instant updates, offline support, and seamless synchronization.
                </GlassCardDescription>
              </GlassCardHeader>
            </GlassCard>
          </div>
        </div>
      </section>

      {/* Technical Details Section */}
      <section className="py-12 sm:py-16 px-4 sm:px-6 bg-muted/20">
        <div className="w-full max-w-6xl mx-auto">
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl font-light tracking-tight mb-3 sm:mb-4">Technical Implementation</h2>
            <p className="text-base sm:text-lg text-muted-foreground font-light px-4">
              Built with modern technologies and best practices
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Frontend */}
            <GlassCard intensity="medium">
              <GlassCardContent className="p-6">
                <h3 className="text-lg font-medium mb-4 gradient-red-purple gradient-text">Frontend Architecture</h3>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                    <span>Next.js 15 with App Router for modern React patterns</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                    <span>TypeScript for type safety and developer experience</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                    <span>Tailwind CSS for utility-first styling</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                    <span>Custom glass morphism design system</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                    <span>TanStack Query for advanced caching and state management</span>
                  </div>
                </div>
              </GlassCardContent>
            </GlassCard>

            {/* Backend */}
            <GlassCard intensity="medium">
              <GlassCardContent className="p-6">
                <h3 className="text-lg font-medium mb-4 gradient-red-purple gradient-text">Backend & Database</h3>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-secondary"></div>
                    <span>Next.js API routes and Server Actions</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-secondary"></div>
                    <span>Neon PostgreSQL for scalable data storage</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-secondary"></div>
                    <span>Drizzle ORM with type-safe database queries</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-secondary"></div>
                    <span>Row Level Security (RLS) for multi-tenant isolation</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-secondary"></div>
                    <span>Connection pooling and query optimization</span>
                  </div>
                </div>
              </GlassCardContent>
            </GlassCard>

            {/* Authentication */}
            <GlassCard intensity="medium">
              <GlassCardContent className="p-6">
                <h3 className="text-lg font-medium mb-4 gradient-red-purple gradient-text">Authentication & Security</h3>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-accent"></div>
                    <span>Stack Auth integration for user management</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-accent"></div>
                    <span>JWT-based authentication with JWKS validation</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-accent"></div>
                    <span>Server-side session management</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-accent"></div>
                    <span>Protected routes and middleware</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-accent"></div>
                    <span>Role-based access control</span>
                  </div>
                </div>
              </GlassCardContent>
            </GlassCard>

            {/* UI/UX Features */}
            <GlassCard intensity="medium">
              <GlassCardContent className="p-6">
                <h3 className="text-lg font-medium mb-4 gradient-red-purple gradient-text">UI/UX Features</h3>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                    <span>Responsive design with mobile-first approach</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                    <span>Glass morphism effects with backdrop filters</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                    <span>Dark/light theme support with system detection</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                    <span>Customizable color themes and preferences</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                    <span>Smooth animations and micro-interactions</span>
                  </div>
                </div>
              </GlassCardContent>
            </GlassCard>

            {/* Advanced Features */}
            <GlassCard intensity="medium">
              <GlassCardContent className="p-6">
                <h3 className="text-lg font-medium mb-4 gradient-red-purple gradient-text">Advanced Functionality</h3>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-secondary"></div>
                    <span>Real-time collaboration with optimistic updates</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-secondary"></div>
                    <span>Drag-and-drop with @hello-pangea/dnd</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-secondary"></div>
                    <span>Hierarchical task organization (spaces/lists/subtasks)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-secondary"></div>
                    <span>Advanced tagging system with color coding</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-secondary"></div>
                    <span>AI-powered chat integration with Vercel AI SDK</span>
                  </div>
                </div>
              </GlassCardContent>
            </GlassCard>

            {/* Performance & Deployment */}
            <GlassCard intensity="medium">
              <GlassCardContent className="p-6">
                <h3 className="text-lg font-medium mb-4 gradient-red-purple gradient-text">Performance & Deployment</h3>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-accent"></div>
                    <span>Aggressive caching with 10-minute stale time</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-accent"></div>
                    <span>Optimistic mutations for instant UI feedback</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-accent"></div>
                    <span>Selective cache invalidation strategies</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-accent"></div>
                    <span>Vercel deployment with edge functions</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-accent"></div>
                    <span>Progressive Web App (PWA) capabilities</span>
                  </div>
                </div>
              </GlassCardContent>
            </GlassCard>
          </div>
        </div>
      </section>

      {/* About Me Section */}
      <section className="py-12 sm:py-16 px-4 sm:px-6">
        <div className="w-full max-w-4xl mx-auto">
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl font-light tracking-tight mb-3 sm:mb-4">About the Developer</h2>
            <p className="text-base sm:text-lg text-muted-foreground font-light px-4">
              Meet the person behind NeoTask
            </p>
          </div>

          <GlassCard intensity="medium">
            <GlassCardContent className="p-6 sm:p-8">
              <div className="flex flex-col md:flex-row gap-6 items-start">
                {/* Profile Image Placeholder */}
                <div className="flex-shrink-0 mx-auto md:mx-0">
                  <div className="w-24 h-24 sm:w-32 sm:h-32 rounded-full bg-gradient-to-r from-primary via-secondary to-accent flex items-center justify-center">
                    <span className="text-2xl sm:text-3xl font-light text-white">MS</span>
                  </div>
                </div>

                {/* Content */}
                <div className="flex-1 text-center md:text-left">
                  <h3 className="text-xl sm:text-2xl font-normal mb-2 gradient-red-purple gradient-text">
                    Myles Stupp
                  </h3>
                  <p className="text-base sm:text-lg text-muted-foreground mb-4">
                    CRM Automation Manager at Shutterstock | Full-Stack Developer
                  </p>

                  <div className="space-y-4 text-muted-foreground">
                    <p>
                      I'm a technology professional with a passion for building innovative solutions that bridge
                      the gap between business needs and technical implementation. Currently working as a CRM
                      Automation Manager at Shutterstock, I specialize in creating automated workflows and
                      systems that enhance operational efficiency.
                    </p>

                    <p>
                      NeoTask represents my journey into modern full-stack development, where I've combined
                      my professional experience in automation and process optimization with cutting-edge
                      web technologies. This project showcases my commitment to continuous learning and
                      my ability to deliver polished, production-ready applications.
                    </p>

                    <p>
                      Based in the Atlanta Metropolitan Area, I'm always excited to connect with fellow
                      developers and explore new opportunities in technology and innovation.
                    </p>
                  </div>

                  {/* Skills Tags */}
                  <div className="flex flex-wrap gap-2 mt-6 justify-center md:justify-start">
                    <span className="px-3 py-1 text-xs rounded-full bg-primary/10 text-primary border border-primary/20">
                      CRM Automation
                    </span>
                    <span className="px-3 py-1 text-xs rounded-full bg-secondary/10 text-secondary border border-secondary/20">
                      Full-Stack Development
                    </span>
                    <span className="px-3 py-1 text-xs rounded-full bg-accent/10 text-accent border border-accent/20">
                      Process Optimization
                    </span>
                    <span className="px-3 py-1 text-xs rounded-full bg-primary/10 text-primary border border-primary/20">
                      React & Next.js
                    </span>
                    <span className="px-3 py-1 text-xs rounded-full bg-secondary/10 text-secondary border border-secondary/20">
                      Database Design
                    </span>
                  </div>

                  {/* LinkedIn Link */}
                  <div className="mt-6">
                    <Button asChild variant="outline" size="sm" className="font-normal">
                      <Link
                        href="https://www.linkedin.com/in/myles-stupp-639bb962/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center"
                      >
                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                        Connect on LinkedIn
                        <ArrowRight className="ml-1 h-3 w-3" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </GlassCardContent>
          </GlassCard>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-16 sm:py-20 px-4 sm:px-6">
        <div className="w-full max-w-4xl mx-auto">
          <GlassCard intensity="strong" className="text-center p-6 sm:p-12">
            <GlassCardContent>
              <div className="space-y-4 sm:space-y-6">
                <h2 className="text-2xl sm:text-3xl font-light tracking-tight gradient-red-purple gradient-text">
                  Ready to transform your productivity?
                </h2>
                <p className="text-lg sm:text-xl text-muted-foreground font-light max-w-2xl mx-auto px-4">
                  Join thousands of teams who have already made the switch to smarter task management.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center">
                  <Button asChild size="lg" className="gradient-red-purple text-white font-normal w-full sm:w-auto">
                    <Link href="/signup">
                      Start Free Today
                      <Sparkles className="ml-2 h-5 w-5" />
                    </Link>
                  </Button>
                  <p className="text-xs sm:text-sm text-muted-foreground text-center">
                    No credit card required • Free forever plan available
                  </p>
                </div>
              </div>
            </GlassCardContent>
          </GlassCard>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-6 sm:py-8 px-4 sm:px-6 border-t border-border/50">
        <div className="w-full max-w-4xl mx-auto">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
            <div className="flex items-center space-x-2 sm:space-x-3">
              <Image
                src="/NeoTask_Icon_N.webp"
                alt="NeoTask"
                width={20}
                height={20}
                className="object-contain sm:w-6 sm:h-6"
              />
              <span className="text-xs sm:text-sm text-muted-foreground text-center sm:text-left">
                © 2024 NeoTask. A portfolio project showcasing modern full-stack development.
              </span>
            </div>
            <div className="flex items-center space-x-4 sm:space-x-6">
              <Link href="/signin" className="text-xs sm:text-sm text-muted-foreground hover:text-foreground transition-colors">
                Sign In
              </Link>
              <Link href="/signup" className="text-xs sm:text-sm text-muted-foreground hover:text-foreground transition-colors">
                Sign Up
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
